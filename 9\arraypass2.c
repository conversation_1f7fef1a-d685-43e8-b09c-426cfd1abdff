#include<stdio.h>

#define MAX 10

int array[MAX], count;

int largest(int num_array[]);

int main(void)
{
    
    for (count = 0; count < MAX; count++)
    {
        printf("enter the %d num:\n", count + 1 );
        scanf("%d", &array[count]);
        if (array[count] == 0)
        {
            count = MAX;
        }
    }
    array[MAX] = 0;
    printf("the best num is:%d", largest(array));
    
    return 0;
}

int largest(int num_array[])
{
    int best_num = 0;
    for (size_t i = 0; num_array[i] != 0 ; i++)
    {
        if (best_num == 0)
        {
            best_num = num_array[i];
        }
        if (num_array[i] > best_num)
        {
            best_num = num_array[i];
        }
    }
    return best_num;
}