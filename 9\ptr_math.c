#include<stdio.h>
#define MAX 10

int i_array[MAX] = {0,1,2,3,4,5,6,7,8,9};
// int t_array[MAX] = {0,1,2,3,4,5,6,7,8,9};
int *i_ptr, count, *t_ptr;
float f_array[MAX] = {.0,.1,.2,.3,.4,.5,.6,.7,.8,.9};
float *f_ptr;

int main(void)
{
    i_ptr = i_array;
    f_ptr = f_array;
    // t_ptr = t_array;

    for (count = 0; count < MAX; count++)
    {
        printf("a--%d\t%f\t%d\n", *i_ptr++, *f_ptr++);
        
        // printf("b--%d\t%f\n", (*i_ptr)++, (*f_ptr)++);
        // printf("c--%d\t%f\n", *(i_ptr++), *(f_ptr++));
    }
    return 0;
}