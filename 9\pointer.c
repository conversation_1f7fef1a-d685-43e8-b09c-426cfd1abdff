#include<stdio.h>

int var = 1;
int *ptr;
int main(void)
{
    ptr = &var;
    printf("var = %d\n", var);
    printf("*ptr = %d\n", *ptr);

    printf("var address = %p\n", &var);
    printf("ptr = %p\n", ptr);

    int array[10];
    printf("array ptr = %p\n", array);
    for (size_t i = 0; i < 10; i++)
    {
        array[i] = i;
    }

    int *ptr2 = array;
    printf("%d", ptr2[8]);
    
    int *ptr3;
    float *ptr4;
    short *ptr5;
    long *ptr6;
    double *ptr7;
    char *ptr8;
    printf("int ptr = %zu\n", sizeof(ptr3));
    printf("float ptr = %zu\n", sizeof(ptr4));
    printf("short ptr = %zu\n", sizeof(ptr5));
    printf("long ptr = %zu\n", sizeof(ptr6));
    printf("double ptr = %zu\n", sizeof(ptr7));
    printf("char ptr = %zu\n", sizeof(ptr8));


    return 0;
}
