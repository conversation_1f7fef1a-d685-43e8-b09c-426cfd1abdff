#include<stdio.h>
#include<stdlib.h>

void clear_kb(void);

int main(void)
{

    FILE *fp;
    float data[5];
    // int count;
    char filename[20];

    puts("Enter 5 floating-point numerical values.");
    for (size_t i = 0; i < 5; i++)
    {
        scanf("%f", &data[i]);
    }

    clear_kb();

    puts("Enter a name for the file.");
    // gets(filename);
    fgets(filename, 20, stdin);
    
    if ((fp = fopen(filename, "w")) == NULL)
    {
        fprintf(stderr, "Error opening file %s", filename);
        exit(1);
    }

    for (size_t i = 0; i < 5; i++)
    {
        fprintf(fp, "\ndata[%d] = %f", i, data[i]);
        fprintf(stdout, "\ndata[%d] = %f", i, data[i]);
    }
    

    fclose(fp);
    printf("\n");
    return 0;
}

void clear_kb(void)
{
    char junk[80];
    fgets(junk, 80, stdin);
}