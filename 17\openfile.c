#include<stdlib.h>
#include<stdio.h>


int main(void)
{
    FILE *fp;
    char ch, filename[40], mode[4];

    while (1)
    {
        puts("\nEnter a filename:");
        gets(filename);
        puts("\nEnter a mode (max 3 characters):");
        gets(mode);

        if ((fp = fopen(filename, mode)) != NULL)
        {
            printf("\nSuccessful openning %s in mode %s.\n", filename, mode);
            if ((ch = getc(stdin)) == 'x')
            {
                break;
            }
            else
            {
                continue;
            }
        }
        else
        {
            fprintf(stderr, "\nEnter opening file %s in mode %s.\n", filename, mode);
            puts("Enter x to exit, any other to try again.");
            if ((ch = getc(stdin)) == 'x')
            {
                break;
            }
            else
            {
                continue;
            }
        }
    }
    

    return 0;
}