#include<stdio.h>
#include<string.h>
#include <stdlib.h>
#include <time.h>
#define ARR_MAX_NUM 10

int main()
{
    srand(time(NULL));
    int array[ARR_MAX_NUM];
    for (size_t i = 0; i < ARR_MAX_NUM; i++)
    {
        array[i] = rand(); // Fill the array with random numbers
        printf("1-%zu-%d\n", i, array[i]);
        // array[i] *= 10;
    }

    int * copy = malloc(sizeof(int) * ARR_MAX_NUM);
    // 数组的定义，他本身就是首地址，所以拷贝的时候不需要再取地址了
    memcpy(copy, array, sizeof(int) * ARR_MAX_NUM);
    for (size_t i = 0; i < ARR_MAX_NUM; i++)
    {
        printf("2-%zu-%d\n", i, copy[i]);
    }

    free(copy);

    return 0;
}



// rand()