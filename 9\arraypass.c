#include <stdio.h>
#define MAX 10

int array[MAX], count;
int largest(int *num_array, int length);
// int largest(int num_array[], int length); 另一种方式传递数组

int main(void)
{

    for (count = 0; count < MAX; count++)
    {
        printf("enter the %d num:\n", count + 1 );
        scanf("%d", &array[count]);
    }
    printf("the best num is:%d", largest(array, MAX));

    return 0;
}

int largest(int * num_array, int length)
{
    int bestNum = 0;
    for (size_t i = 0; i < length; i++)
    {
        if (bestNum == 0)
        {
            bestNum = num_array[i];
        }
        if (num_array[i] > bestNum)
        {
            bestNum = num_array[i];
        }
    }

    return bestNum;
}