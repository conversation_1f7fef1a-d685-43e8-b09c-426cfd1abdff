#include<stdlib.h>
#include<stdio.h>
#include<string.h>

struct data {
    char name[20];
    struct data *next;
};

typedef struct data PERSON;
typedef PERSON *LINK;// LINK是一个指向PERSON结构的指针

int main(void)
{
    LINK head = NULL;
    LINK new = NULL;
    LINK current = NULL;

    new = (LINK)malloc(sizeof(PERSON));
    new->next = head;
    head = new;
    strcpy(new->name, "LANSHANGHAO");

    current = head;
    while (current->next != NULL)
    {
        current = current->next;
    }
    new = (LINK)malloc(sizeof(PERSON));
    current->next = new;
    new->next = NULL;
    strcpy(new->name, "CHENSHAN");

    new = (LINK)malloc(sizeof(PERSON));
    new->next = head->next;
    head->next = new;
    strcpy(new->name, "CHENGZI");

    current = head;
    while (current != NULL)
    {
        printf("%s\n", current->name);
        current = current->next;
    }
    
    

    return 0;
}