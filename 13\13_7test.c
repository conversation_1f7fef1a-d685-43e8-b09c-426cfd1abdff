#include<stdio.h>
#define ADD 1
#define SUBTRACT 2
#define MULTIPLY 3
#define DIVIDE 4


int get_choice(void);
int choice;
int num1, num2;
float result;
int main(void)
{
    choice = 0;
    do
    {
        choice = get_choice();
        if (choice != 0) {
            printf("please enter two num\n");
            scanf("%d %d", &num1, &num2);
            switch (choice)
            {
                case ADD:
                    result = num1 + num2;
                    break;
                case SUBTRACT:
                    result = num1 - num2;
                    break;
                case MULTIPLY:
                    result = num1 * num2;
                    break;
                case DIVIDE:
                    result = num1 / num2;
                    break;
                default:
                    break;
            }
            printf("result = %.2f\n", result);
        }
    } while (choice != 0);
    
    return 0;
}

int get_choice(void)
{
    int choice = 0;
    printf("enter 1=add, 2=subtract, 3=multiply and 4=divide\n");
    scanf("%d", &choice);
    return choice;
}
