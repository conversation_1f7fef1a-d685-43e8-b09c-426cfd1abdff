#include<stdio.h>
#include<stdlib.h>

#ifndef NULL
#define NULL 0
#endif

struct list
{
    int ch;
    struct list *next_rec;
};

typedef struct list LIST;
typedef LIST *LISTPTR;

LISTPTR add_to_list(int, LISTPTR);
void show_list(LISTPTR);
void free_memory_list(LISTPTR);

int main(void)
{

    LISTPTR head = NULL;
    int input;
    int now_idx = 1;
    // char trash[256];
    // gets(trash);
    do
    {
        printf("enter %dth index:\n", now_idx);
        scanf("%d", &input);
        // printf("\nenter %dth index:\n", now_idx);
        if (input == 0)
        {
            break;
        }
        head = add_to_list(input, head);
        now_idx++;
    } while (input != 0);
        
    show_list(head);
    free_memory_list(head);
    
    return 0;
}

LISTPTR add_to_list(int addValue, LISTPTR head)
{
    LISTPTR tmp_rec = NULL;
    LISTPTR prev_rec = NULL;
    LISTPTR new_rec = (LISTPTR)malloc(sizeof(LIST));

    if (!new_rec)
    {
        printf("\nUnable to allocate memory!\n");
        exit(1);
    }

    new_rec->ch = addValue;
    new_rec->next_rec = NULL;

    if (head == NULL)
    {
        head = new_rec;
        new_rec->next_rec = NULL;
    } 
    else 
    {
        // 在第一个元素之前
        if (new_rec->ch < head->ch)
        {
            new_rec->next_rec = head;
            head = new_rec;
        }
        else
        {
            tmp_rec = head;
            int has_join = 0;
            while (tmp_rec != NULL)
            {
                // 如果新节点比旧节点小，要放到旧的节点之前
                if (new_rec->ch < tmp_rec->ch)
                {
                    new_rec->next_rec = tmp_rec;// 新节点插到前面
                    prev_rec->next_rec = new_rec;// 把前一个节点的下一个节点
                    return head;
                }

                prev_rec = tmp_rec;
                tmp_rec = tmp_rec->next_rec;
            }

            prev_rec->next_rec = new_rec;
            new_rec->next_rec = NULL;
        }
    }
    return head;
}

// LISTPTR add_to_list(int addValue, LISTPTR head) {
//     LISTPTR tmp_rec = head;
//     LISTPTR prev_rec = NULL;
//     LISTPTR new_rec = (LISTPTR)malloc(sizeof(LIST));

//     if (!new_rec) {
//         printf("\nUnable to allocate memory!\n");
//         exit(1);
//     }

//     new_rec->ch = addValue;
//     new_rec->next_rec = NULL;

//     if (head == NULL) {
//         return new_rec;
//     }

//     // 插入到头部
//     if (addValue < head->ch) {
//         new_rec->next_rec = head;
//         return new_rec;
//     }

//     // 遍历查找插入位置
//     while (tmp_rec != NULL && addValue >= tmp_rec->ch) {
//         prev_rec = tmp_rec;
//         tmp_rec = tmp_rec->next_rec;
//     }

//     // 插入
//     new_rec->next_rec = tmp_rec;
//     prev_rec->next_rec = new_rec;

//     return head;
// }

void show_list(LISTPTR head) 
{
    LISTPTR current = head;
    while (current != NULL)
    {
        
        printf("\nch:%d\tmy_rec:%p\tnext_rec:%p",current->ch, current, current->next_rec);
        current = current->next_rec;
    }
}

void free_memory_list(LISTPTR head) 
{
    LISTPTR current = head;
    while (current->next_rec != NULL)
    {
        LISTPTR tmp;
        tmp = current->next_rec;
        free(current);
        current = tmp;
    }
}